<script setup lang="ts">
// 保单附件组件
import { message } from 'ant-design-vue';
import { onMounted, ref, defineProps, defineEmits, watch, computed } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';
import { UploadFile } from '@haierbusiness-front/common-libs';

// 类型定义
interface InsuranceAttachmentItem {
  tempId: string; // 临时ID
  serialNumber: number; // 序号（不传递给父组件）
  insuranceId: string; // 保险产品ID
  insuranceName: string; // 保险产品名称
  paths: string[]; // 附件路径数组
  attachmentFiles: UploadFile[]; // 用于UI显示的附件文件对象
}

// 文件上传相关常量
const SUPPORTED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
const FILE_SIZE_LIMIT = 10; // MB
const UPLOAD_ACCEPT = '.pdf,.jpg,.jpeg,.png,.gif,.doc,.docx';

const props = defineProps({
  attachmentList: {
    type: Array as () => InsuranceAttachmentItem[],
    default: () => [],
  },
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  schemeType: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['attachmentEmit']);

// 暴露给父组件的方法
defineExpose({
  getAttachmentDataForSubmit: () => getAttachmentDataForSubmit(props.attachmentList),
});

// 响应式数据
const uploadLoading = ref(false);
const previewVisible = ref(false);
const previewFile = ref<UploadFile | null>(null);
const previewFileName = ref('');

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 计算属性：获取所有附件文件
const allAttachmentFiles = computed(() => {
  const files: UploadFile[] = [];
  props.attachmentList.forEach((item) => {
    if (item.attachmentFiles && item.attachmentFiles.length > 0) {
      files.push(...item.attachmentFiles);
    }
  });
  return files;
});

// 获取文件显示名称
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '';

  const maxLength = 15;
  if (fileName.length <= maxLength) return fileName;

  const extension = fileName.split('.').pop() || '';
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';

  return `${truncatedName}.${extension}`;
};

// 文件验证
const validateFile = (file: File): boolean => {
  const isValidType =
    SUPPORTED_FILE_TYPES.includes(file.type) ||
    file.name.toLowerCase().endsWith('.pdf') ||
    file.name.toLowerCase().endsWith('.doc') ||
    file.name.toLowerCase().endsWith('.docx');

  if (!isValidType) {
    message.error('只支持上传 PDF、图片、Word 文档格式的文件！');
    return false;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`文件大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return false;
  }

  return true;
};

// 处理附件上传
const handleAttachmentUpload = async (options: any) => {
  const { file } = options;

  if (!validateFile(file)) {
    return;
  }

  uploadLoading.value = true;

  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fileApi.upload(formData);

    // 确保路径正确拼接
    const filePath = response.path || '';
    const fullUrl = filePath ? (filePath.startsWith('/') ? baseUrl + filePath : baseUrl + '/' + filePath) : '';

    const fileObj: UploadFile = {
      uid: file.uid || Date.now().toString(),
      name: file.name,
      status: 'done',
      url: fullUrl,
      filePath: filePath,
      fileName: file.name,
    };

    // 如果没有附件列表项，创建一个默认项
    let updatedList = [...props.attachmentList];
    if (updatedList.length === 0) {
      updatedList = [
        {
          tempId: `insurance_default_${Date.now()}`,
          serialNumber: 1,
          insuranceId: 'default',
          insuranceName: '保单附件',
          paths: [],
          attachmentFiles: [],
        },
      ];
    }

    // 添加到第一个项目中
    updatedList[0] = {
      ...updatedList[0],
      attachmentFiles: [...(updatedList[0].attachmentFiles || []), fileObj],
      paths: [...(updatedList[0].paths || []), filePath],
    };

    emit('attachmentEmit', updatedList);
    message.success('文件上传成功');
  } catch (error) {
    console.error('文件上传失败:', error);
    message.error('文件上传失败，请重试');
  } finally {
    uploadLoading.value = false;
  }
};

// 移除附件
const handleRemoveAttachment = (file: UploadFile) => {
  const updatedList = props.attachmentList.map((item) => {
    const newAttachmentFiles = item.attachmentFiles.filter((f) => f.uid !== file.uid);
    const newPaths = item.paths.filter((path) => path !== file.filePath);

    return {
      ...item,
      attachmentFiles: newAttachmentFiles,
      paths: newPaths,
    };
  });

  emit('attachmentEmit', updatedList);
  message.success('文件删除成功');
};

// 预览文件
const handlePreviewFile = (file: UploadFile, customTitle?: string) => {
  previewFile.value = file;
  previewFileName.value = customTitle || file.name;
  previewVisible.value = true;
};

// 关闭预览
const handlePreviewCancel = () => {
  console.log('12312');

  // 强制关闭弹框
  previewVisible.value = false;
  // 延迟清理数据，确保弹框完全关闭
  setTimeout(() => {
    previewFile.value = null;
    previewFileName.value = '';
  }, 100);
};

// 处理弹框开关状态变化
const handleModalOpenChange = (visible: boolean) => {
  if (!visible) {
    // 确保数据被清理
    previewFile.value = null;
    previewFileName.value = '';
  }
};

// 下载文件
const handleDownloadFile = () => {
  if (previewFile.value && previewFile.value.url) {
    window.open(previewFile.value.url, '_blank');
  }
};

// 获取提交数据
const getAttachmentDataForSubmit = (attachmentList: InsuranceAttachmentItem[]) => {
  return attachmentList.map((item) => ({
    insuranceId: item.insuranceId,
    paths: item.paths,
  }));
};

// 监听保险数据变化，初始化附件列表
watch(
  () => props.schemeItem,
  (newObj) => {
    if (newObj?.insurances && newObj.insurances.length > 0 && props.attachmentList.length === 0) {
      const initialList = newObj.insurances.map((insurance: any, index: number) => ({
        tempId: `insurance_${insurance.id || index}_${Date.now()}`,
        serialNumber: index + 1,
        insuranceId: insurance.id || insurance.productId,
        insuranceName: insurance.insuranceName || '保险产品',
        paths: insurance.attachmentPaths || [],
        attachmentFiles: (insurance.attachmentPaths || []).map((path: string, pathIndex: number) => ({
          uid: `${insurance.id}_${pathIndex}`,
          name: path.split('/').pop() || `附件${pathIndex + 1}`,
          status: 'done',
          url: path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path,
          filePath: path,
          fileName: path.split('/').pop() || `附件${pathIndex + 1}`,
        })),
      }));

      emit('attachmentEmit', initialList);
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

onMounted(async () => {});
</script>

<template>
  <div class="insurance-attachment-section">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>保单附件</span>
      <span class="tip-text">（请上传保险产品相关的保单文件）</span>
    </div>

    <div class="simple-upload-wrapper">
      <div class="upload-row">
        <div class="upload-label">保单上传:</div>
        <div class="upload-content">
          <!-- 已上传文件标签 -->
          <div class="file-tags" v-if="allAttachmentFiles.length > 0">
            <a-tag
              v-for="file in allAttachmentFiles"
              :key="file.uid"
              closable
              class="file-tag"
              @click="() => handlePreviewFile(file)"
              @close="() => handleRemoveAttachment(file)"
            >
              {{ getFileDisplayName(file.name) }}
            </a-tag>
          </div>

          <!-- 上传按钮 -->
          <a-upload
            :file-list="[]"
            :custom-request="handleAttachmentUpload"
            :multiple="true"
            :show-upload-list="false"
            :accept="UPLOAD_ACCEPT"
          >
            <a-button type="primary" :loading="uploadLoading">
              <upload-outlined />
              上传
            </a-button>
          </a-upload>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal
      v-model:open="previewVisible"
      title="文件预览"
      width="80%"
      @cancel="handlePreviewCancel"
      @update:open="handleModalOpenChange"
      :maskClosable="true"
      :keyboard="true"
    >
      <div class="preview-content">
        <div class="preview-body">
          <template v-if="previewFile && previewFile.url">
            <img
              v-if="
                previewFile.name &&
                (previewFile.name.toLowerCase().includes('.jpg') ||
                  previewFile.name.toLowerCase().includes('.jpeg') ||
                  previewFile.name.toLowerCase().includes('.png') ||
                  previewFile.name.toLowerCase().includes('.gif'))
              "
              :src="previewFile.url"
              alt="预览图片"
              style="max-width: 100%; max-height: 500px; object-fit: contain"
            />
            <iframe
              v-else-if="previewFile.name && previewFile.name.toLowerCase().includes('.pdf')"
              :src="previewFile.url"
              style="width: 100%; height: 500px; border: none; pointer-events: auto"
              @load="() => {}"
            ></iframe>
            <div v-else class="file-download">
              <p>无法预览此文件类型，请下载查看</p>
              <a-button type="primary" @click="handleDownloadFile"> 下载文件 </a-button>
            </div>
          </template>
          <template v-else>
            <div class="no-file">
              <p>文件信息：{{ previewFileName }}</p>
              <p>暂无可预览的文件内容</p>
            </div>
          </template>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.insurance-attachment-section {
  position: relative;
  margin-bottom: 24px;

  .interact_title {
    margin-left: -24px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    height: 25px;
    line-height: 25px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #1d2129;

    .interact_shu {
      width: 4px;
      height: 20px;
      background: #1868db;
    }

    .tip-text {
      margin-left: 16px;
      font-size: 12px;
      color: #ff4d4f;
      font-weight: normal;
    }
  }

  .mr20 {
    margin-right: 20px;
  }

  .simple-upload-wrapper {
    width: 100%;
    margin-bottom: 16px;

    .upload-row {
      display: flex;
      align-items: center;
      gap: 16px;
      min-height: 40px;

      .upload-label {
        font-size: 14px;
        color: #333;
        font-weight: 500;
        white-space: nowrap;
      }

      .upload-content {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
        flex: 1;

        .file-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-right: 8px;

          .file-tag {
            cursor: pointer;
            margin: 0;
            border-radius: 4px;
            font-size: 12px;
            padding: 4px 8px;
            background-color: #f0f0f0;
            border: 1px solid #d9d9d9;
            color: #333;

            &:hover {
              background-color: #e6f7ff;
              border-color: #91d5ff;
              color: #1890ff;
            }
          }
        }

        .ant-upload {
          .ant-btn {
            height: 32px;
            font-size: 14px;
          }
        }
      }
    }
  }

  // 无边框输入框样式
  :deep(.borderless-input) {
    border: none !important;
    box-shadow: none !important;
    background: transparent;

    &:hover,
    &:focus {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input {
      border: none;
      box-shadow: none;
      background: transparent;

      &:hover,
      &:focus {
        border: none;
        box-shadow: none;
      }
    }
  }

  // 预览弹框样式
  .preview-content {
    .preview-body {
      text-align: center;

      .file-download,
      .no-file {
        padding: 40px 20px;
        color: #666;

        p {
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style>
